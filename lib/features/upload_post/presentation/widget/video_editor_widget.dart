// ignore_for_file: unused_element

import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/upload_post/presentation/widget/crop_page.dart';
import 'package:video_editor/video_editor.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';

//-------------------//
//VIDEO EDITOR SCREEN//
//-------------------//
class VideoEditor extends StatefulWidget {
  const VideoEditor({super.key, required this.file});

  final File file;

  @override
  State<VideoEditor> createState() => _VideoEditorState();
}

class _VideoEditorState extends State<VideoEditor> {
  final ValueNotifier<double> _exportingProgress = ValueNotifier<double>(0.0);
  final _isExporting = ValueNotifier<bool>(false);
  final double height = 60;

  late final VideoEditorController _controller = VideoEditorController.file(
    widget.file,
    minDuration: const Duration(seconds: 1),
    maxDuration: const Duration(hours: 1),
  );

  @override
  void initState() {
    super.initState();
    _controller.initialize(aspectRatio: 9 / 16).then((_) {
      if (mounted) {
        setState(() {});
      }
    }).catchError((error) {
      if (mounted) {
        Navigator.pop(context);
      }
    }, test: (e) => e is VideoMinDurationError);
  }

  @override
  void dispose() {
    _exportingProgress.dispose();
    _isExporting.dispose();
    _controller.dispose();
    super.dispose();
  }

  void _showErrorSnackBar(String message) => toastification.show(
        type: ToastificationType.error,
        showProgressBar: false,
        title: Text(message),
        autoCloseDuration: const Duration(seconds: 1),
      );

  void _exportVideo() async {
    _exportingProgress.value = 0;
    _isExporting.value = true;

    try {
      // Get temporary directory for output
      final Directory tempDir = await getTemporaryDirectory();
      final String outputPath = '${tempDir.path}/edited_video_${const Uuid().v4()}.mp4';

      Logger.lOG('Starting video export...');
      Logger.lOG('Output path: $outputPath');

      // Create video editor config with all transformations (crop, rotation, trim)
      final config = VideoFFmpegVideoEditorConfig(
        _controller,
        name: 'edited_video_${const Uuid().v4()}',
        outputDirectory: tempDir.path,
        // Enable all filters (crop, rotation, trim)
        isFiltersEnabled: true,
      );

      // Get the FFmpeg command and output path
      final FFmpegVideoEditorExecute execute = await config.getExecuteConfig();

      Logger.lOG('FFmpeg command: ${execute.command}');
      Logger.lOG('Expected output: ${execute.outputPath}');

      // Execute FFmpeg command
      final session = await FFmpegKit.execute(execute.command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        // Verify the exported file
        final File outputFile = File(execute.outputPath);
        if (await outputFile.exists()) {
          final int fileSize = await outputFile.length();

          if (fileSize > 1024) {
            // Ensure file is larger than 1KB to be valid
            Logger.lOG('✅ Video export successful: ${execute.outputPath} (${fileSize ~/ 1024} KB)');
            _isExporting.value = false;

            if (mounted) {
              Navigator.pop(context, execute.outputPath);
            }
          } else {
            throw Exception('Exported file is too small, likely corrupted');
          }
        } else {
          throw Exception('Output file was not created at ${execute.outputPath}');
        }
      } else {
        final logs = await session.getAllLogsAsString();
        throw Exception('FFmpeg export failed with return code: $returnCode\nLogs: $logs');
      }
    } catch (e) {
      Logger.lOG('❌ Video export failed: $e');
      _isExporting.value = false;
      _showErrorSnackBar("Error exporting video: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _controller.initialized
          ? SafeArea(
              child: Stack(
                children: [
                  Column(
                    children: [
                      _topNavBar(),
                      Expanded(
                        child: DefaultTabController(
                          length: 2,
                          child: Column(
                            children: [
                              Expanded(
                                child: TabBarView(
                                  physics: const NeverScrollableScrollPhysics(),
                                  children: [
                                    Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        CropGridViewer.preview(controller: _controller),
                                        AnimatedBuilder(
                                          animation: _controller.video,
                                          builder: (_, __) => AnimatedOpacity(
                                            opacity: _controller.isPlaying ? 0 : 1,
                                            duration: kThemeAnimationDuration,
                                            child: GestureDetector(
                                              onTap: _controller.video.play,
                                              child: Container(
                                                width: 40,
                                                height: 40,
                                                decoration: const BoxDecoration(
                                                  color: Colors.white,
                                                  shape: BoxShape.circle,
                                                ),
                                                child: const Icon(
                                                  Icons.play_arrow,
                                                  color: Colors.black,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    CoverViewer(controller: _controller)
                                  ],
                                ),
                              ),
                              Container(
                                height: 200,
                                margin: const EdgeInsets.only(top: 10),
                                child: Column(
                                  children: [
                                    TabBar(
                                      tabs: [
                                        Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                                          const Padding(padding: EdgeInsets.all(5), child: Icon(Icons.content_cut)),
                                          Text(
                                            'Trim',
                                            style: Theme.of(context).textTheme.bodyMedium,
                                          ),
                                        ]),
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            const Padding(padding: EdgeInsets.all(5), child: Icon(Icons.video_label)),
                                            Text(
                                              'Cover',
                                              style: Theme.of(context).textTheme.bodyMedium,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    Expanded(
                                      child: TabBarView(
                                        physics: const NeverScrollableScrollPhysics(),
                                        children: [
                                          Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: _trimSlider(),
                                          ),
                                          _coverSelection(),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              ValueListenableBuilder<double>(
                                valueListenable: _exportingProgress,
                                builder: (_, double value, __) => LinearProgressIndicator(
                                  value: value,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              buildSizedBoxH(16)
                            ],
                          ),
                        ),
                      )
                    ],
                  )
                ],
              ),
            )
          : Center(child: LoadingAnimationWidget()),
    );
  }

  Widget _topNavBar() {
    return SafeArea(
      child: SizedBox(
        height: height,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  NavigatorService.goBack();
                },
                child: const Row(
                  children: [Icon(Icons.close)],
                ),
              ),
              IconButton(
                onPressed: () => _controller.rotate90Degrees(RotateDirection.left),
                icon: const Icon(Icons.rotate_left),
              ),
              IconButton(
                onPressed: () => _controller.rotate90Degrees(RotateDirection.right),
                icon: const Icon(Icons.rotate_right),
              ),
              IconButton(
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute<void>(
                    builder: (context) => CropPage(controller: _controller),
                  ),
                ),
                icon: const Icon(Icons.crop),
              ),
              ValueListenableBuilder<bool>(
                valueListenable: _isExporting,
                builder: (_, bool isExporting, __) => InkWell(
                  onTap: isExporting ? null : _exportVideo,
                  child: Text("Next",
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: isExporting
                              ? Theme.of(context).colorScheme.primary.withOpacity(0.2)
                              : Theme.of(context).colorScheme.primary)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String formatter(Duration duration) => [
        duration.inMinutes.remainder(60).toString().padLeft(2, '0'),
        duration.inSeconds.remainder(60).toString().padLeft(2, '0')
      ].join(":");

  List<Widget> _trimSlider() {
    return [
      AnimatedBuilder(
        animation: Listenable.merge([
          _controller,
          _controller.video,
        ]),
        builder: (_, __) {
          final int duration = _controller.videoDuration.inSeconds;
          final double pos = _controller.trimPosition * duration;
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: height / 4.2.w),
            child: Row(children: [
              Text(formatter(Duration(seconds: pos.toInt()))),
              const Expanded(child: SizedBox()),
              AnimatedOpacity(
                opacity: _controller.isTrimming ? 1 : 0,
                duration: kThemeAnimationDuration,
                child: Row(mainAxisSize: MainAxisSize.min, children: [
                  Text(formatter(_controller.startTrim)),
                  buildSizedBoxW(10),
                  Text(formatter(_controller.endTrim)),
                ]),
              ),
            ]),
          );
        },
      ),
      Container(
        width: MediaQuery.of(context).size.width,
        margin: EdgeInsets.symmetric(vertical: height / 4.h),
        child: TrimSlider(
          controller: _controller,
          height: height,
          horizontalMargin: height / 4.w,
          child: TrimTimeline(
            controller: _controller,
            padding: EdgeInsets.only(top: 10.h),
          ),
        ),
      )
    ];
  }

  Widget _coverSelection() {
    return SingleChildScrollView(
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(15),
          child: CoverSelection(
            controller: _controller,
            size: height + 10,
            quantity: 8,
            selectedCoverBuilder: (cover, size) {
              return Stack(
                alignment: Alignment.center,
                children: [
                  cover,
                  Icon(
                    Icons.check_circle,
                    color: const CoverSelectionStyle().selectedBorderColor,
                  )
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
