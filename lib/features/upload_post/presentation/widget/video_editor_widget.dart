// ignore_for_file: unused_element

import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/upload_post/presentation/widget/crop_page.dart';
import 'package:video_editor/video_editor.dart';
import 'package:easy_video_editor/easy_video_editor.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

//-------------------//
//VIDEO EDITOR SCREEN//
//-------------------//
class VideoEditor extends StatefulWidget {
  const VideoEditor({super.key, required this.file});

  final File file;

  @override
  State<VideoEditor> createState() => _VideoEditorState();
}

class _VideoEditorState extends State<VideoEditor> {
  final ValueNotifier<double> _exportingProgress = ValueNotifier<double>(0.0);
  final _isExporting = ValueNotifier<bool>(false);
  final double height = 60;

  late final VideoEditorController _controller = VideoEditorController.file(
    widget.file,
    minDuration: const Duration(seconds: 1),
    maxDuration: const Duration(hours: 1),
  );

  @override
  void initState() {
    super.initState();
    _controller.initialize(aspectRatio: 9 / 16).then((_) {
      if (mounted) {
        setState(() {});
      }
    }).catchError((error) {
      if (mounted) {
        Navigator.pop(context);
      }
    }, test: (e) => e is VideoMinDurationError);
  }

  @override
  void dispose() {
    _exportingProgress.dispose();
    _isExporting.dispose();
    _controller.dispose();
    super.dispose();
  }

  void _showErrorSnackBar(String message) => toastification.show(
        type: ToastificationType.error,
        showProgressBar: false,
        title: Text(message),
        autoCloseDuration: const Duration(seconds: 1),
      );

  void _exportVideo() async {
    _exportingProgress.value = 0;
    _isExporting.value = true;

    try {
      // Check if only trimming is applied (no rotation or significant crop)
      final bool isOnlyTrimming = _isOnlyTrimmingApplied();

      Logger.lOG('Starting video export...');
      Logger.lOG('Only trimming applied: $isOnlyTrimming');

      if (isOnlyTrimming) {
        // Use easy_video_editor for simple trimming (more stable)
        await _exportWithEasyVideoEditor();
      } else {
        // Show warning that advanced transformations aren't supported
        _showAdvancedTransformationWarning();
      }
    } catch (e) {
      Logger.lOG('❌ Video export failed: $e');
      _isExporting.value = false;
      _showErrorSnackBar("Error exporting video: $e");
    }
  }

  bool _isOnlyTrimmingApplied() {
    // Check if rotation is applied
    final bool hasRotation = _controller.rotation != 0;

    // Check if significant crop is applied (allowing for minor adjustments)
    final bool hasSignificantCrop = _controller.minCrop.dx > 0.05 ||
        _controller.minCrop.dy > 0.05 ||
        _controller.maxCrop.dx < 0.95 ||
        _controller.maxCrop.dy < 0.95;

    // Check if trimming is applied
    final bool hasTrimming = _controller.isTrimmed;

    Logger.lOG('Rotation applied: $hasRotation');
    Logger.lOG('Significant crop applied: $hasSignificantCrop');
    Logger.lOG('Trimming applied: $hasTrimming');

    return !hasRotation && !hasSignificantCrop;
  }

  Future<void> _exportWithEasyVideoEditor() async {
    try {
      final Directory tempDir = await getTemporaryDirectory();
      final String outputPath = '${tempDir.path}/edited_video_${const Uuid().v4()}.mp4';

      Logger.lOG('Exporting with easy_video_editor to: $outputPath');

      // Get trim values
      final startTime = _controller.startTrim.inMilliseconds / 1000.0;
      final endTime = _controller.endTrim.inMilliseconds / 1000.0;

      Logger.lOG('Trim: ${startTime}s to ${endTime}s');

      // Use easy_video_editor for trimming
      final editor = VideoEditorBuilder(videoPath: widget.file.path).trim(
        startTimeMs: _controller.startTrim.inMilliseconds,
        endTimeMs: _controller.endTrim.inMilliseconds,
      );

      final result = await editor.export(
        outputPath: outputPath,
        onProgress: (progress) {
          _exportingProgress.value = progress;
        },
      );

      if (result != null && result.isNotEmpty) {
        // Verify the exported file
        final File outputFile = File(result);
        if (await outputFile.exists()) {
          final int fileSize = await outputFile.length();

          if (fileSize > 1024) {
            Logger.lOG('✅ Video export successful: $result (${fileSize ~/ 1024} KB)');
            _isExporting.value = false;

            if (mounted) {
              Navigator.pop(context, result);
            }
          } else {
            throw Exception('Exported file is too small, likely corrupted');
          }
        } else {
          throw Exception('Output file was not created');
        }
      } else {
        throw Exception('Easy video editor returned null result');
      }
    } catch (e) {
      Logger.lOG('❌ Easy video editor export failed: $e');
      rethrow;
    }
  }

  void _showAdvancedTransformationWarning() {
    _isExporting.value = false;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Advanced Editing Not Supported'),
        content: const Text(
            'Rotation and crop operations are not currently supported for video export due to device compatibility issues. Only trimming is available.\n\nWould you like to export with just the trim settings?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              // Reset transformations except trim and export
              await _exportTrimOnly();
            },
            child: const Text('Export Trim Only'),
          ),
        ],
      ),
    );
  }

  Future<void> _exportTrimOnly() async {
    _isExporting.value = true;
    try {
      await _exportWithEasyVideoEditor();
    } catch (e) {
      Logger.lOG('❌ Trim-only export failed: $e');
      _isExporting.value = false;
      _showErrorSnackBar("Error exporting video: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _controller.initialized
          ? SafeArea(
              child: Stack(
                children: [
                  Column(
                    children: [
                      _topNavBar(),
                      Expanded(
                        child: DefaultTabController(
                          length: 2,
                          child: Column(
                            children: [
                              Expanded(
                                child: TabBarView(
                                  physics: const NeverScrollableScrollPhysics(),
                                  children: [
                                    Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        CropGridViewer.preview(controller: _controller),
                                        AnimatedBuilder(
                                          animation: _controller.video,
                                          builder: (_, __) => AnimatedOpacity(
                                            opacity: _controller.isPlaying ? 0 : 1,
                                            duration: kThemeAnimationDuration,
                                            child: GestureDetector(
                                              onTap: _controller.video.play,
                                              child: Container(
                                                width: 40,
                                                height: 40,
                                                decoration: const BoxDecoration(
                                                  color: Colors.white,
                                                  shape: BoxShape.circle,
                                                ),
                                                child: const Icon(
                                                  Icons.play_arrow,
                                                  color: Colors.black,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    CoverViewer(controller: _controller)
                                  ],
                                ),
                              ),
                              Container(
                                height: 200,
                                margin: const EdgeInsets.only(top: 10),
                                child: Column(
                                  children: [
                                    TabBar(
                                      tabs: [
                                        Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                                          const Padding(padding: EdgeInsets.all(5), child: Icon(Icons.content_cut)),
                                          Text(
                                            'Trim',
                                            style: Theme.of(context).textTheme.bodyMedium,
                                          ),
                                        ]),
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            const Padding(padding: EdgeInsets.all(5), child: Icon(Icons.video_label)),
                                            Text(
                                              'Cover',
                                              style: Theme.of(context).textTheme.bodyMedium,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    Expanded(
                                      child: TabBarView(
                                        physics: const NeverScrollableScrollPhysics(),
                                        children: [
                                          Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: _trimSlider(),
                                          ),
                                          _coverSelection(),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              ValueListenableBuilder<double>(
                                valueListenable: _exportingProgress,
                                builder: (_, double value, __) => LinearProgressIndicator(
                                  value: value,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              buildSizedBoxH(16)
                            ],
                          ),
                        ),
                      )
                    ],
                  )
                ],
              ),
            )
          : Center(child: LoadingAnimationWidget()),
    );
  }

  Widget _topNavBar() {
    return SafeArea(
      child: SizedBox(
        height: height,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  NavigatorService.goBack();
                },
                child: const Row(
                  children: [Icon(Icons.close)],
                ),
              ),
              IconButton(
                onPressed: () => _controller.rotate90Degrees(RotateDirection.left),
                icon: const Icon(Icons.rotate_left),
              ),
              IconButton(
                onPressed: () => _controller.rotate90Degrees(RotateDirection.right),
                icon: const Icon(Icons.rotate_right),
              ),
              IconButton(
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute<void>(
                    builder: (context) => CropPage(controller: _controller),
                  ),
                ),
                icon: const Icon(Icons.crop),
              ),
              ValueListenableBuilder<bool>(
                valueListenable: _isExporting,
                builder: (_, bool isExporting, __) => InkWell(
                  onTap: isExporting ? null : _exportVideo,
                  child: Text("Next",
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: isExporting
                              ? Theme.of(context).colorScheme.primary.withOpacity(0.2)
                              : Theme.of(context).colorScheme.primary)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String formatter(Duration duration) => [
        duration.inMinutes.remainder(60).toString().padLeft(2, '0'),
        duration.inSeconds.remainder(60).toString().padLeft(2, '0')
      ].join(":");

  List<Widget> _trimSlider() {
    return [
      AnimatedBuilder(
        animation: Listenable.merge([
          _controller,
          _controller.video,
        ]),
        builder: (_, __) {
          final int duration = _controller.videoDuration.inSeconds;
          final double pos = _controller.trimPosition * duration;
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: height / 4.2.w),
            child: Row(children: [
              Text(formatter(Duration(seconds: pos.toInt()))),
              const Expanded(child: SizedBox()),
              AnimatedOpacity(
                opacity: _controller.isTrimming ? 1 : 0,
                duration: kThemeAnimationDuration,
                child: Row(mainAxisSize: MainAxisSize.min, children: [
                  Text(formatter(_controller.startTrim)),
                  buildSizedBoxW(10),
                  Text(formatter(_controller.endTrim)),
                ]),
              ),
            ]),
          );
        },
      ),
      Container(
        width: MediaQuery.of(context).size.width,
        margin: EdgeInsets.symmetric(vertical: height / 4.h),
        child: TrimSlider(
          controller: _controller,
          height: height,
          horizontalMargin: height / 4.w,
          child: TrimTimeline(
            controller: _controller,
            padding: EdgeInsets.only(top: 10.h),
          ),
        ),
      )
    ];
  }

  Widget _coverSelection() {
    return SingleChildScrollView(
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(15),
          child: CoverSelection(
            controller: _controller,
            size: height + 10,
            quantity: 8,
            selectedCoverBuilder: (cover, size) {
              return Stack(
                alignment: Alignment.center,
                children: [
                  cover,
                  Icon(
                    Icons.check_circle,
                    color: const CoverSelectionStyle().selectedBorderColor,
                  )
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
